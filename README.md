# AI测试用例生成器

一个基于AI的智能测试用例生成平台，支持从流程图、思维导图、UI截图、PDF文档和OpenAPI规范生成高质量测试用例。


## ✨ 功能特性

- 🤖 **AI驱动**: 基于Qwen和DeepSeek大模型智能生成测试用例
- 📊 **多格式支持**: 支持图像、PDF、JSON、YAML等多种输入格式
- 🎯 **智能解析**: 自动识别流程图、思维导图和UI界面元素
- 📋 **多种输出**: 支持Excel、XMind、JSON等多种输出格式
- 🚀 **高性能**: 异步处理，支持大文件和批量操作
- 🔒 **安全可靠**: 完善的文件验证和错误处理机制

## 🏗️ 项目架构

```
AI_testcase/
├── backend/                 # FastAPI后端服务
│   ├── main.py             # 应用入口
│   ├── start.py            # 启动脚本
│   ├── config.py           # 配置管理
│   ├── requirements.txt    # Python依赖
│   ├── routers/            # API路由
│   ├── services/           # 业务逻辑
│   ├── models/             # 数据模型
│   ├── utils/              # 工具函数
│   ├── uploads/            # 文件上传目录
│   ├── results/            # 结果输出目录
│   └── logs/               # 日志文件
├── frontend/               # React前端应用
│   ├── package.json        # Node.js依赖
│   ├── src/                # 源代码
│   └── public/             # 静态资源
├── start.sh                # Linux/macOS启动脚本
├── start.bat               # Windows启动脚本
└── README.md               # 项目文档
```

## 🛠️ 技术栈

### 后端技术
- **框架**: FastAPI + Uvicorn
- **语言**: Python 3.8+
- **AI集成**: AutoGen + OpenAI兼容API
- **数据处理**: Pandas, OpenPyXL, XMind
- **文件处理**: PyPDF2, PDFPlumber, Pillow
- **配置管理**: Pydantic Settings
- **日志系统**: Structlog

### 前端技术
- **框架**: React 18
- **UI库**: Material-UI (MUI)
- **构建工具**: Create React App
- **HTTP客户端**: Axios
- **文件上传**: React Dropzone
- **内容渲染**: React Markdown, React Syntax Highlighter

## 🚀 快速开始

### 环境要求
- **Python**: 3.8+ (推荐 3.11+)
- **Node.js**: 16+ (推荐 18+)
- **npm**: 最新版本

### 一键启动（推荐）

本项目提供了便捷的启动脚本，支持一键启动前后端服务：

#### Linux/macOS 用户
```bash
# 给脚本添加执行权限（首次使用）
chmod +x start.sh

# 一键启动所有服务
./start.sh

# 查看服务状态
./start.sh status

# 停止所有服务
./start.sh stop
```

#### Windows 用户
```cmd
# 一键启动所有服务
start.bat

# 查看服务状态
start.bat status

# 停止所有服务
start.bat stop
```

### 启动脚本功能

| 命令 | 功能说明 |
|------|----------|
| `start` | 启动前后端服务（默认） |
| `stop` | 停止所有服务 |
| `restart` | 重启所有服务 |
| `status` | 查看服务运行状态 |
| `install` | 安装项目依赖 |
| `logs [backend\|frontend]` | 查看日志文件 |
| `help` | 显示帮助信息 |

### 首次使用配置

1. **安装依赖**
   ```bash
   # Linux/macOS
   ./start.sh install

   # Windows
   start.bat install
   ```

2. **配置环境变量**
   ```bash
   # 复制环境变量模板
   cp backend/.env.example backend/.env

   # 编辑配置文件，添加必要的API密钥
   # TESTCASE_QWEN_API_KEY=your_qwen_api_key
   # TESTCASE_DEEPSEEK_API_KEY=your_deepseek_api_key
   ```

3. **启动服务**
   ```bash
   # 启动所有服务
   ./start.sh start  # 或 start.bat start
   ```

### 服务地址

启动成功后，可通过以下地址访问：

- 🌐 **前端应用**: http://localhost:3000
- 🔧 **后端API**: http://localhost:8000
- 📚 **API文档**: http://localhost:8000/docs
- ❤️ **健康检查**: http://localhost:8000/api/health

## 🔧 手动启动（开发者）

如果需要手动启动服务进行开发调试：

### 后端启动
```bash
cd backend

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate  # Linux/macOS
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt

# 启动服务
python start.py
# 或 uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

### 前端启动
```bash
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm start
```

## ⚙️ 配置说明

### 环境变量配置

| 变量名 | 必需 | 默认值 | 说明 |
|--------|------|--------|------|
| `TESTCASE_QWEN_API_KEY` | ✅ | - | Qwen API密钥 |
| `TESTCASE_DEEPSEEK_API_KEY` | ✅ | - | DeepSeek API密钥 |
| `TESTCASE_HOST` | ❌ | 0.0.0.0 | 服务器主机地址 |
| `TESTCASE_PORT` | ❌ | 8000 | 服务器端口 |
| `TESTCASE_DEBUG` | ❌ | false | 调试模式 |
| `TESTCASE_ALLOWED_ORIGINS` | ❌ | localhost:3000 | CORS允许的来源 |
| `TESTCASE_MAX_FILE_SIZE` | ❌ | 52428800 | 最大文件大小(50MB) |
| `TESTCASE_LOG_LEVEL` | ❌ | INFO | 日志级别 |

### API接口说明

主要API端点：
- `GET /` - 欢迎信息
- `GET /api/ping` - 基础健康检查
- `GET /api/health` - 详细健康检查
- `POST /api/generate-test-cases` - 生成测试用例
- `GET /docs` - Swagger API文档

## 🐳 生产部署

### Docker部署
```bash
# 构建镜像
docker build -t ai-testcase-generator .

# 运行容器
docker run -p 8000:8000 --env-file backend/.env ai-testcase-generator
```

### Nginx反向代理
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        client_max_body_size 50M;
    }
}
```

## 🔍 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用
   lsof -i :8000  # macOS/Linux
   netstat -ano | findstr :8000  # Windows

   # 使用脚本停止服务
   ./start.sh stop
   ```

2. **API密钥错误**
   - 检查 `backend/.env` 文件中的API密钥配置
   - 确保密钥格式正确且有效

3. **依赖安装失败**
   ```bash
   # 重新安装依赖
   ./start.sh install

   # 或手动清理后重装
   rm -rf backend/venv frontend/node_modules
   ./start.sh install
   ```

4. **文件上传失败**
   - 检查文件大小是否超过限制（默认50MB）
   - 确认文件格式是否支持
   - 查看后端日志获取详细错误信息

### 查看日志
```bash
# 查看所有日志
./start.sh logs

# 查看后端日志
./start.sh logs backend

# 查看前端日志
./start.sh logs frontend
```

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目！

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

---

**技术支持**: 如遇问题请查看日志文件或联系技术支持团队
**课程咨询**: 微信 huice666
